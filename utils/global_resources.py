"""
全局资源管理模块
统一管理需要缓存的资源，避免重复实例化
"""

import streamlit as st
from utils.semantic_search_engine import SemanticSearchEngine
from utils.product_data_manager import ProductDataManager


@st.cache_resource
def get_global_search_engine():
    """
    获取全局搜索引擎实例
    
    使用@st.cache_resource确保整个应用只有一个搜索引擎实例，
    避免在不同页面重复加载470MB的模型
    
    Returns:
        SemanticSearchEngine: 语义搜索引擎实例
    """
    return SemanticSearchEngine()


@st.cache_resource  
def get_global_product_manager():
    """
    获取全局商品数据管理器实例
    
    使用@st.cache_resource确保整个应用只有一个数据管理器实例
    
    Returns:
        ProductDataManager: 商品数据管理器实例
    """
    return ProductDataManager()


def clear_global_caches():
    """
    清除所有全局缓存
    
    在需要重新加载资源时调用，比如：
    - 重建搜索索引后
    - 数据结构发生重大变化后
    """
    st.cache_resource.clear()
