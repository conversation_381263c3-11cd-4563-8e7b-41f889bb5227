"""
语义搜索引擎
基于sentence-transformers和faiss实现多语言语义搜索
"""

import numpy as np
import faiss
import pickle
import os
from typing import List, Dict, Tuple, Optional
from sentence_transformers import SentenceTransformer
import logging
from .product_data_manager import ProductDataManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SemanticSearchEngine:
    """语义搜索引擎"""
    
    def __init__(self,
                 model_name: str = "paraphrase-multilingual-MiniLM-L12-v2",
                 index_file_path: str = "data/products.faiss",
                 embeddings_file_path: str = "data/product_embeddings.pkl",
                 product_data_manager: Optional[ProductDataManager] = None):
        """
        初始化语义搜索引擎
        
        Args:
            model_name: 预训练模型名称
            index_file_path: Faiss索引文件路径
            embeddings_file_path: 嵌入向量文件路径
            product_data_manager: 商品数据管理器
        """
        self.model_name = model_name
        self.index_file_path = index_file_path
        self.embeddings_file_path = embeddings_file_path
        
        # 初始化商品数据管理器
        if product_data_manager is None:
            self.product_manager = ProductDataManager()
        else:
            self.product_manager = product_data_manager
        
        # 初始化模型和索引
        self.model = None
        self.index = None
        self.product_data = []
        self.embeddings = None
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(index_file_path), exist_ok=True)
        
    def _load_model(self):
        """加载sentence-transformers模型"""
        if self.model is None:
            logger.info(f"正在加载模型: {self.model_name}")
            try:
                # 尝试使用device_map参数来避免meta tensor问题
                import torch
                device = "cpu"  # 强制使用CPU
                self.model = SentenceTransformer(self.model_name, device=device)
                logger.info("模型加载完成")
            except Exception as e:
                logger.error(f"模型加载失败: {e}")
                # 尝试备用方案
                try:
                    logger.info("尝试备用加载方案...")
                    self.model = SentenceTransformer(self.model_name, trust_remote_code=True)
                    logger.info("备用方案加载成功")
                except Exception as e2:
                    logger.error(f"备用方案也失败: {e2}")
                    raise e2
    
    def _create_search_text(self, product: Dict) -> str:
        """
        极简版：根据简化的CSV文件，生成搜索文本

        新的简化策略：
        - 只使用5个核心字段：product_id, product_name_zh, product_name_es, location, keywords
        - 将所有相关词汇统一放入keywords字段
        - 使用最简单直接的文本组合方式

        Args:
            product: 商品数据字典

        Returns:
            简化的搜索文本字符串
        """
        # 安全地获取字符串字段，处理NaN值
        name_zh = str(product.get('product_name_zh', '') or '').strip()
        name_es = str(product.get('product_name_es', '') or '').strip()
        keywords = str(product.get('keywords', '') or '').strip()

        # 将所有文本信息用最简单、最直接的方式组合起来
        # 使用连字符和空格，确保模型能区分独立的词语
        search_text = f"{name_zh} - {name_es} - {keywords.replace(',', ' ')}"

        # 移除多余的空格和开头的连字符，保持干净
        return search_text.strip().lstrip('-').strip()
    
    def build_index(self, force_rebuild: bool = False) -> bool:
        """
        构建搜索索引
        
        Args:
            force_rebuild: 是否强制重建索引
            
        Returns:
            构建是否成功
        """
        try:
            # 检查是否需要重建索引
            if not force_rebuild and self._index_exists():
                logger.info("索引文件已存在，跳过构建")
                return True
            
            logger.info("开始构建搜索索引...")
            
            # 加载模型
            self._load_model()
            
            # 获取商品数据
            self.product_data = self.product_manager.get_all_products_for_search()
            
            if not self.product_data:
                logger.warning("没有商品数据，无法构建索引")
                return False
            
            # 创建搜索文本
            search_texts = []
            for product in self.product_data:
                search_text = self._create_search_text(product)
                search_texts.append(search_text)
            
            logger.info(f"正在为 {len(search_texts)} 个商品生成嵌入向量...")
            
            # 生成嵌入向量
            self.embeddings = self.model.encode(search_texts, convert_to_numpy=True)
            
            # 创建Faiss索引
            dimension = self.embeddings.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
            
            # 标准化向量（用于余弦相似度）
            faiss.normalize_L2(self.embeddings)
            
            # 添加向量到索引
            self.index.add(self.embeddings)
            
            # 保存索引和嵌入向量
            self._save_index()
            self._save_embeddings()
            
            logger.info(f"索引构建完成，包含 {len(self.product_data)} 个商品")
            return True
            
        except Exception as e:
            logger.error(f"构建索引失败: {e}")
            return False
    
    def load_index(self) -> bool:
        """
        加载已存在的索引
        
        Returns:
            加载是否成功
        """
        try:
            if not self._index_exists():
                logger.warning("索引文件不存在，需要先构建索引")
                return False
            
            # 加载模型
            self._load_model()
            
            # 加载索引
            self.index = faiss.read_index(self.index_file_path)
            
            # 加载嵌入向量和商品数据
            self._load_embeddings()
            
            # 重新加载商品数据（确保数据是最新的）
            self.product_data = self.product_manager.get_all_products_for_search()
            
            logger.info(f"索引加载完成，包含 {len(self.product_data)} 个商品")
            return True
            
        except Exception as e:
            logger.error(f"加载索引失败: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[Dict, float]]:
        """
        执行语义搜索
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表，每个元素为(商品数据, 相似度分数)
        """
        try:
            # 确保索引已加载
            if self.index is None:
                if not self.load_index():
                    logger.error("无法加载索引，请先构建索引")
                    return []
            
            # 确保模型已加载
            if self.model is None:
                self._load_model()
            
            # 生成查询向量
            query_embedding = self.model.encode([query], convert_to_numpy=True)
            faiss.normalize_L2(query_embedding)
            
            # 执行搜索
            scores, indices = self.index.search(query_embedding, top_k)
            
            # 整理结果
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(self.product_data):
                    product = self.product_data[idx]
                    results.append((product, float(score)))
            
            logger.info(f"搜索查询 '{query}' 返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def _index_exists(self) -> bool:
        """检查索引文件是否存在"""
        return (os.path.exists(self.index_file_path) and 
                os.path.exists(self.embeddings_file_path))
    
    def _save_index(self):
        """保存Faiss索引"""
        faiss.write_index(self.index, self.index_file_path)
        logger.info(f"索引已保存到: {self.index_file_path}")
    
    def _save_embeddings(self):
        """保存嵌入向量和商品数据"""
        data_to_save = {
            'embeddings': self.embeddings,
            'product_data': self.product_data
        }
        with open(self.embeddings_file_path, 'wb') as f:
            pickle.dump(data_to_save, f)
        logger.info(f"嵌入向量已保存到: {self.embeddings_file_path}")
    
    def _load_embeddings(self):
        """加载嵌入向量和商品数据"""
        with open(self.embeddings_file_path, 'rb') as f:
            data = pickle.load(f)
            self.embeddings = data['embeddings']
            # 注意：这里不加载product_data，因为我们要使用最新的数据
        logger.info("嵌入向量加载完成")
    
    def get_index_info(self) -> Dict:
        """
        获取索引信息
        
        Returns:
            索引信息字典
        """
        info = {
            'model_name': self.model_name,
            'index_exists': self._index_exists(),
            'product_count': 0,
            'index_dimension': 0
        }
        
        if self.index is not None:
            info['product_count'] = self.index.ntotal
            info['index_dimension'] = self.index.d
        elif self._index_exists():
            # 尝试加载索引获取信息
            try:
                temp_index = faiss.read_index(self.index_file_path)
                info['product_count'] = temp_index.ntotal
                info['index_dimension'] = temp_index.d
            except:
                pass
        
        return info
