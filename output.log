nohup: ignoring input

Collecting usage statistics. To deactivate, set browser.gatherUsageStats to false.


  You can now view your Streamlit app in your browser.

  Local URL: http://localhost:8501
  Network URL: http://*********:8501
  External URL: http://*************:8501

2025-08-05 20:05:27.194 File changed in watched directory: /www/wwwroot/firstPyProject/utils/__pycache__/semantic_search_engine.cpython-313.pyc.139999489797040
2025-08-05 20:05:27.196 File changed in watched directory: /www/wwwroot/firstPyProject/utils/__pycache__
INFO:faiss.loader:Loading faiss with AVX2 support.
INFO:faiss.loader:Successfully loaded faiss with AVX2 support.
INFO:faiss:Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/www/server/pyporject_evn/versions/3.13.5/lib/python3.13/threading.py", line 1043, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 213, in run
    self.dispatch_events(self.event_queue)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 391, in dispatch_events
    handler.dispatch(event)
    ~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/events.py", line 217, in dispatch
    getattr(self, f"on_{event.event_type}")(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 422, in on_modified
    self.handle_path_change_event(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 390, in handle_path_change_event
    modification_time = util.path_modification_time(
        abs_changed_path, changed_path_info.allow_nonexistent
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 94, in path_modification_time
    return _do_with_retries(
        lambda: os.stat(path).st_mtime,
        (FileNotFoundError, PermissionError),
        path,
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 175, in _do_with_retries
    return orig_fn()
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 95, in <lambda>
    lambda: os.stat(path).st_mtime,
            ~~~~~~~^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/www/wwwroot/firstPyProject/utils/__pycache__/semantic_search_engine.cpython-313.pyc.139999489797040'
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
INFO:utils.semantic_search_engine:模型加载完成
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:正在为 11 个商品生成嵌入向量...

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00,  1.07it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.07it/s]
INFO:utils.semantic_search_engine:索引已保存到: data/products.faiss
INFO:utils.semantic_search_engine:嵌入向量已保存到: data/product_embeddings.pkl
INFO:utils.semantic_search_engine:索引构建完成，包含 11 个商品
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:嵌入向量加载完成
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:索引加载完成，包含 11 个商品
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00, 43.94it/s]
INFO:utils.semantic_search_engine:搜索查询 '行李箱' 返回 5 个结果
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功保存 12 条商品数据到 data/products.csv
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功加载 12 条商品数据
INFO:utils.product_data_manager:成功保存 13 条商品数据到 data/products.csv
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
INFO:utils.semantic_search_engine:模型加载完成
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.semantic_search_engine:正在为 13 个商品生成嵌入向量...

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:01<00:00,  1.28s/it]
Batches: 100%|██████████| 1/1 [00:01<00:00,  1.28s/it]
INFO:utils.semantic_search_engine:索引已保存到: data/products.faiss
INFO:utils.semantic_search_engine:嵌入向量已保存到: data/product_embeddings.pkl
INFO:utils.semantic_search_engine:索引构建完成，包含 13 个商品
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
INFO:utils.product_data_manager:成功加载 13 条商品数据
