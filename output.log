nohup: ignoring input

Collecting usage statistics. To deactivate, set browser.gatherUsageStats to false.


  You can now view your Streamlit app in your browser.

  Local URL: http://localhost:8501
  Network URL: http://*********:8501
  External URL: http://*************:8501

INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:加载索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
ERROR:utils.semantic_search_engine:无法加载索引，请先构建索引
INFO:utils.semantic_search_engine:模型加载完成
INFO:utils.semantic_search_engine:嵌入向量加载完成
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:索引加载完成，包含 11 个商品

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00,  1.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.54it/s]
INFO:utils.semantic_search_engine:搜索查询 '都菜刀' 返回 5 个结果
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00, 40.38it/s]
INFO:utils.semantic_search_engine:搜索查询 '行李箱' 返回 5 个结果

Batches:   0%|          | 0/1 [00:00<?, ?it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.09it/s]
INFO:utils.semantic_search_engine:搜索查询 'Estoy buscando el cuchillo.' 返回 5 个结果
