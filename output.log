nohup: ignoring input

Collecting usage statistics. To deactivate, set browser.gatherUsageStats to false.


  You can now view your Streamlit app in your browser.

  Local URL: http://localhost:8501
  Network URL: http://*********:8501
  External URL: http://*************:8501

INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:加载索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
ERROR:utils.semantic_search_engine:无法加载索引，请先构建索引
INFO:utils.semantic_search_engine:模型加载完成
INFO:utils.semantic_search_engine:嵌入向量加载完成
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:索引加载完成，包含 11 个商品

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00,  1.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.54it/s]
INFO:utils.semantic_search_engine:搜索查询 '都菜刀' 返回 5 个结果
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据

Batches:   0%|          | 0/1 [00:00<?, ?it/s]/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/torch/nn/modules/module.py:1762: FutureWarning: `encoder_attention_mask` is deprecated and will be removed in version 4.55.0 for `BertSdpaSelfAttention.forward`.
  return forward_call(*args, **kwargs)

Batches: 100%|██████████| 1/1 [00:00<00:00, 40.38it/s]
INFO:utils.semantic_search_engine:搜索查询 '行李箱' 返回 5 个结果

Batches:   0%|          | 0/1 [00:00<?, ?it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.09it/s]
INFO:utils.semantic_search_engine:搜索查询 'Estoy buscando el cuchillo.' 返回 5 个结果
2025-08-05 19:46:48.531 File changed in watched directory: /www/wwwroot/firstPyProject/utils/global_resources.py
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/www/server/pyporject_evn/versions/3.13.5/lib/python3.13/threading.py", line 1043, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 213, in run
    self.dispatch_events(self.event_queue)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 391, in dispatch_events
    handler.dispatch(event)
    ~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/events.py", line 217, in dispatch
    getattr(self, f"on_{event.event_type}")(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 419, in on_created
    self.handle_path_change_event(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 390, in handle_path_change_event
    modification_time = util.path_modification_time(
        abs_changed_path, changed_path_info.allow_nonexistent
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 94, in path_modification_time
    return _do_with_retries(
        lambda: os.stat(path).st_mtime,
        (FileNotFoundError, PermissionError),
        path,
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 175, in _do_with_retries
    return orig_fn()
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 95, in <lambda>
    lambda: os.stat(path).st_mtime,
            ~~~~~~~^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/www/wwwroot/firstPyProject/utils/__pycache__/global_resources.cpython-313.pyc.139994265241648'
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:加载索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
ERROR:utils.semantic_search_engine:无法加载索引，请先构建索引
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:加载索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:加载索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
ERROR:utils.semantic_search_engine:无法加载索引，请先构建索引
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:构建索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:构建索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Use pytorch device_name: cpu
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
ERROR:utils.semantic_search_engine:构建索引失败: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
