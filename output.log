nohup: ignoring input

Collecting usage statistics. To deactivate, set browser.gatherUsageStats to false.


  You can now view your Streamlit app in your browser.

  Local URL: http://localhost:8501
  Network URL: http://*********:8501
  External URL: http://*************:8501

INFO:faiss.loader:Loading faiss with AVX2 support.
INFO:faiss.loader:Successfully loaded faiss with AVX2 support.
INFO:faiss:Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
Exception in thread Thread-1:
Traceback (most recent call last):
  File "/www/server/pyporject_evn/versions/3.13.5/lib/python3.13/threading.py", line 1043, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 213, in run
    self.dispatch_events(self.event_queue)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/observers/api.py", line 391, in dispatch_events
    handler.dispatch(event)
    ~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/watchdog/events.py", line 217, in dispatch
    getattr(self, f"on_{event.event_type}")(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 419, in on_created
    self.handle_path_change_event(event)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py", line 405, in handle_path_change_event
    new_md5 = util.calc_md5_with_blocking_retries(
        abs_changed_path,
        glob_pattern=changed_path_info.glob_pattern,
        allow_nonexistent=changed_path_info.allow_nonexistent,
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 66, in calc_md5_with_blocking_retries
    content = _do_with_retries(
        lambda: _get_file_content(path),
        (FileNotFoundError, PermissionError),
        path,
    )
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 175, in _do_with_retries
    return orig_fn()
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 67, in <lambda>
    lambda: _get_file_content(path),
            ~~~~~~~~~~~~~~~~~^^^^^^
  File "/www/wwwroot/firstPyProject/new_venv/lib/python3.13/site-packages/streamlit/watcher/util.py", line 102, in _get_file_content
    with open(file_path, "rb") as f:
         ~~~~^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/www/wwwroot/firstPyProject/utils/__pycache__/semantic_search_engine.cpython-313.pyc.139918090818480'
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.product_data_manager:成功加载 11 条商品数据
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.semantic_search_engine:正在加载模型: paraphrase-multilingual-MiniLM-L12-v2
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
INFO:utils.semantic_search_engine:模型加载完成
INFO:utils.product_data_manager:成功加载 11 条商品数据
ERROR:utils.semantic_search_engine:构建索引失败: 'float' object has no attribute 'strip'
INFO:utils.semantic_search_engine:开始构建搜索索引...
INFO:utils.product_data_manager:成功加载 11 条商品数据
ERROR:utils.semantic_search_engine:构建索引失败: 'float' object has no attribute 'strip'
