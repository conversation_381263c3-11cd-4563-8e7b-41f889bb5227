"""
语音识别组件
基于Web Speech API的Streamlit自定义组件
"""

import os
import streamlit.components.v1 as components
from typing import Optional, Dict, List

# 创建组件 - 使用HTML方式
def _get_component_html(languages, selected_language, placeholder, height):
    """生成组件的HTML代码"""

    # 语言选项HTML
    language_options = ""
    for lang in languages:
        selected = "selected" if lang["code"] == selected_language else ""
        language_options += f'<option value="{lang["code"]}" {selected}>{lang["name"]}</option>'

    html_code = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                margin: 0;
                padding: 15px;
                background-color: #ffffff;
            }}

            .speech-container {{
                background: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                padding: 20px;
            }}

            .language-selector {{
                margin-bottom: 15px;
            }}

            .language-search {{
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-bottom: 8px;
                box-sizing: border-box;
            }}

            .language-dropdown {{
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                background: white;
                box-sizing: border-box;
            }}

            .speech-controls {{
                display: flex;
                align-items: center;
                gap: 15px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }}

            .speech-button {{
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                gap: 8px;
                background-color: #ff4b4b;
                color: white;
            }}

            .speech-button:hover {{
                background-color: #ff3333;
            }}

            .speech-button.listening {{
                background-color: #ff6b6b;
                animation: pulse 1.5s infinite;
            }}

            .speech-button:disabled {{
                background-color: #ccc;
                cursor: not-allowed;
            }}

            @keyframes pulse {{
                0% {{ opacity: 1; }}
                50% {{ opacity: 0.7; }}
                100% {{ opacity: 1; }}
            }}

            .status-indicator {{
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                background-color: #e8f5e8;
                color: #2d5a2d;
            }}

            .status-listening {{
                background-color: #fff3cd;
                color: #856404;
            }}

            .status-error {{
                background-color: #f8d7da;
                color: #721c24;
            }}

            .result-area {{
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 12px;
                min-height: 50px;
                background-color: #f8f9fa;
                font-size: 14px;
                line-height: 1.4;
            }}

            .result-placeholder {{
                color: #6c757d;
                font-style: italic;
            }}

            .interim-result {{
                color: #6c757d;
                font-style: italic;
            }}

            .final-result {{
                color: #212529;
                font-weight: 500;
            }}

            .error-message {{
                color: #dc3545;
                font-size: 12px;
                margin-top: 8px;
                display: none;
            }}

            .mic-icon {{
                width: 16px;
                height: 16px;
            }}
        </style>
    </head>
    <body>
        <div class="speech-container">
            <div class="language-selector">
                <input type="text" class="language-search" placeholder="搜索语言..." id="languageSearch">
                <select class="language-dropdown" id="languageSelect">
                    {language_options}
                </select>
            </div>

            <div class="speech-controls">
                <button class="speech-button" id="speechButton">
                    <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                    开始录音
                </button>

                <div class="status-indicator" id="statusIndicator">
                    准备就绪
                </div>
            </div>

            <div class="result-area" id="resultArea">
                <div class="result-placeholder">{placeholder}</div>
            </div>

            <div class="error-message" id="errorMessage"></div>
        </div>

        <script>
            // 语音识别实现
            class SpeechRecognitionComponent {{
                constructor() {{
                    this.recognition = null;
                    this.isListening = false;
                    this.selectedLanguage = '{selected_language}';
                    this.finalTranscript = '';
                    this.interimTranscript = '';

                    this.initializeElements();
                    this.initializeSpeechRecognition();
                    this.setupEventListeners();
                }}

                initializeElements() {{
                    this.languageSearch = document.getElementById('languageSearch');
                    this.languageSelect = document.getElementById('languageSelect');
                    this.speechButton = document.getElementById('speechButton');
                    this.statusIndicator = document.getElementById('statusIndicator');
                    this.resultArea = document.getElementById('resultArea');
                    this.errorMessage = document.getElementById('errorMessage');
                }}

                initializeSpeechRecognition() {{
                    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {{
                        this.showError('您的浏览器不支持语音识别功能。请使用Chrome、Edge或Safari浏览器。');
                        this.speechButton.disabled = true;
                        return;
                    }}

                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();

                    this.recognition.continuous = true;
                    this.recognition.interimResults = true;
                    this.recognition.maxAlternatives = 1;
                    this.recognition.lang = this.selectedLanguage;

                    this.recognition.onstart = () => {{
                        this.isListening = true;
                        this.updateUI('listening');
                        this.hideError();
                    }};

                    this.recognition.onresult = (event) => {{
                        let interimTranscript = '';
                        let finalTranscript = this.finalTranscript;

                        for (let i = event.resultIndex; i < event.results.length; i++) {{
                            const transcript = event.results[i][0].transcript;
                            if (event.results[i].isFinal) {{
                                finalTranscript += transcript;
                            }} else {{
                                interimTranscript += transcript;
                            }}
                        }}

                        this.finalTranscript = finalTranscript;
                        this.interimTranscript = interimTranscript;
                        this.updateResult();

                        // 通知父窗口
                        if (window.parent) {{
                            window.parent.postMessage({{
                                type: 'speechResult',
                                transcript: finalTranscript + interimTranscript,
                                isFinal: interimTranscript === '',
                                language: this.selectedLanguage
                            }}, '*');
                        }}
                    }};

                    this.recognition.onerror = (event) => {{
                        let errorMsg = '语音识别出错';
                        switch (event.error) {{
                            case 'no-speech':
                                errorMsg = '没有检测到语音，请重试';
                                break;
                            case 'audio-capture':
                                errorMsg = '无法访问麦克风，请检查权限设置';
                                break;
                            case 'not-allowed':
                                errorMsg = '麦克风权限被拒绝，请允许访问麦克风';
                                break;
                            case 'network':
                                errorMsg = '网络错误，请检查网络连接';
                                break;
                        }}
                        this.showError(errorMsg);
                        this.stopListening();
                    }};

                    this.recognition.onend = () => {{
                        this.isListening = false;
                        this.updateUI('ready');
                    }};
                }}

                setupEventListeners() {{
                    this.speechButton.addEventListener('click', () => {{
                        if (this.isListening) {{
                            this.stopListening();
                        }} else {{
                            this.startListening();
                        }}
                    }});

                    this.languageSelect.addEventListener('change', (e) => {{
                        this.selectedLanguage = e.target.value;
                        if (this.recognition) {{
                            this.recognition.lang = this.selectedLanguage;
                        }}
                    }});

                    this.languageSearch.addEventListener('input', (e) => {{
                        this.filterLanguages(e.target.value);
                    }});
                }}

                filterLanguages(query) {{
                    const options = this.languageSelect.options;
                    for (let i = 0; i < options.length; i++) {{
                        const option = options[i];
                        const text = option.textContent.toLowerCase();
                        const value = option.value.toLowerCase();
                        const match = text.includes(query.toLowerCase()) || value.includes(query.toLowerCase());
                        option.style.display = match ? '' : 'none';
                    }}
                }}

                startListening() {{
                    if (!this.recognition) return;

                    this.finalTranscript = '';
                    this.interimTranscript = '';
                    this.recognition.lang = this.selectedLanguage;

                    try {{
                        this.recognition.start();
                    }} catch (error) {{
                        this.showError('启动语音识别失败，请重试');
                    }}
                }}

                stopListening() {{
                    if (this.recognition && this.isListening) {{
                        this.recognition.stop();
                    }}
                }}

                updateUI(state) {{
                    const button = this.speechButton;
                    const status = this.statusIndicator;

                    if (state === 'listening') {{
                        button.classList.add('listening');
                        button.innerHTML = `
                            <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                            </svg>
                            正在录音...
                        `;
                        status.className = 'status-indicator status-listening';
                        status.textContent = '正在听取语音...';
                    }} else {{
                        button.classList.remove('listening');
                        button.innerHTML = `
                            <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                            </svg>
                            开始录音
                        `;
                        status.className = 'status-indicator';
                        status.textContent = '准备就绪';
                    }}
                }}

                updateResult() {{
                    const resultArea = this.resultArea;

                    if (this.finalTranscript || this.interimTranscript) {{
                        resultArea.innerHTML = '';

                        if (this.finalTranscript) {{
                            const finalSpan = document.createElement('span');
                            finalSpan.className = 'final-result';
                            finalSpan.textContent = this.finalTranscript;
                            resultArea.appendChild(finalSpan);
                        }}

                        if (this.interimTranscript) {{
                            const interimSpan = document.createElement('span');
                            interimSpan.className = 'interim-result';
                            interimSpan.textContent = this.interimTranscript;
                            resultArea.appendChild(interimSpan);
                        }}
                    }} else {{
                        resultArea.innerHTML = '<div class="result-placeholder">{placeholder}</div>';
                    }}
                }}

                showError(message) {{
                    this.errorMessage.textContent = message;
                    this.errorMessage.style.display = 'block';

                    const status = this.statusIndicator;
                    status.className = 'status-indicator status-error';
                    status.textContent = '出现错误';
                }}

                hideError() {{
                    this.errorMessage.style.display = 'none';
                }}
            }}

            // 初始化
            document.addEventListener('DOMContentLoaded', () => {{
                new SpeechRecognitionComponent();
            }});
        </script>
    </body>
    </html>
    """

    return html_code

def speech_recognition_component(
    languages: List[Dict[str, str]] = None,
    selected_language: str = "zh-CN",
    placeholder: str = "点击开始语音识别...",
    key: Optional[str] = None,
    height: int = 200
):
    """
    语音识别组件

    Args:
        languages: 支持的语言列表，格式为 [{"code": "zh-CN", "name": "中文"}]
        selected_language: 默认选择的语言代码
        placeholder: 占位符文本
        key: 组件的唯一标识
        height: 组件高度

    Returns:
        dict: 包含识别结果的字典
    """
    if languages is None:
        languages = get_common_languages()

    # 生成HTML组件
    html_code = _get_component_html(languages, selected_language, placeholder, height)

    # 使用HTML组件
    component_value = components.html(
        html_code,
        height=height,
        scrolling=False
    )

    return component_value

# 便捷函数
def get_common_languages():
    """获取常用语言列表"""
    return [
        {"code": "zh-CN", "name": "中文 (普通话)"},
        {"code": "zh-TW", "name": "中文 (台灣)"},
        {"code": "en-US", "name": "English (US)"},
        {"code": "en-GB", "name": "English (UK)"},
        {"code": "es-ES", "name": "Español (España)"},
        {"code": "es-MX", "name": "Español (México)"},
        {"code": "es-AR", "name": "Español (Argentina)"},
        {"code": "fr-FR", "name": "Français (France)"},
        {"code": "de-DE", "name": "Deutsch"},
        {"code": "it-IT", "name": "Italiano"},
        {"code": "pt-BR", "name": "Português (Brasil)"},
        {"code": "pt-PT", "name": "Português (Portugal)"},
        {"code": "ru-RU", "name": "Русский"},
        {"code": "ja-JP", "name": "日本語"},
        {"code": "ko-KR", "name": "한국어"},
        {"code": "ar-SA", "name": "العربية"},
        {"code": "hi-IN", "name": "हिन्दी"},
        {"code": "th-TH", "name": "ไทย"},
        {"code": "vi-VN", "name": "Tiếng Việt"},
    ]

def search_languages(query: str, all_languages: List[Dict[str, str]] = None):
    """
    搜索语言
    
    Args:
        query: 搜索关键词
        all_languages: 所有语言列表
        
    Returns:
        List[Dict]: 匹配的语言列表
    """
    if all_languages is None:
        all_languages = get_common_languages()
    
    query = query.lower()
    results = []
    
    for lang in all_languages:
        if (query in lang["name"].lower() or 
            query in lang["code"].lower()):
            results.append(lang)
    
    return results
