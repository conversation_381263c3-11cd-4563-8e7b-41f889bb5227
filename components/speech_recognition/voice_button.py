"""
简洁的语音输入按钮组件
"""

import streamlit as st
import streamlit.components.v1 as components
from typing import Optional, Dict

def get_button_text(language_code: str) -> str:
    """
    根据语言代码返回对应的按钮文字
    
    Args:
        language_code: 语言代码，如 'zh-CN', 'es-ES' 等
        
    Returns:
        str: 按钮文字
    """
    # 支持的语言文字映射
    button_texts = {
        # 中文
        'zh-CN': '语音输入',
        'zh-TW': '語音輸入',
        'zh-HK': '語音輸入',
        
        # 西班牙语
        'es-ES': 'Voz',
        'es-MX': 'Voz',
        'es-AR': 'Voz',
        'es-CO': 'Voz',
        'es-CL': 'Voz',
        'es-PE': 'Voz',
        
        # 法语
        'fr-FR': 'Voix',
        'fr-CA': 'Voix',
        
        # 德语
        'de-DE': 'Sprache',
        'de-AT': 'Sprache',
        'de-CH': 'Sprache',
        
        # 意大利语
        'it-IT': 'Voce',
        'it-CH': 'Voce',
    }
    
    # 如果找到对应语言，返回对应文字，否则返回英文
    return button_texts.get(language_code, 'Voice')

def voice_input_button(
    language_code: str = "zh-CN",
    key: Optional[str] = None,
    placeholder: str = "点击开始语音输入...",
    target_input_key: str = "search_input"
) -> Optional[str]:
    """
    语音输入按钮组件

    Args:
        language_code: 语言代码
        key: 组件唯一标识
        placeholder: 占位符文本
        target_input_key: 目标文本输入框的key

    Returns:
        str: 识别到的文字，如果没有则返回None
    """

    button_text = get_button_text(language_code)

    # 创建一个唯一的组件ID
    component_id = f"voice_btn_{key}" if key else "voice_btn_default"
    
    # 生成HTML组件
    html_code = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            }}
            
            .voice-button-container {{
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 4px;
            }}
            
            .voice-button {{
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                border: 1px solid #ff4b4b;
                border-radius: 4px;
                background-color: white;
                color: #ff4b4b;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                white-space: nowrap;
            }}
            
            .voice-button:hover {{
                background-color: #ff4b4b;
                color: white;
            }}
            
            .voice-button.listening {{
                background-color: #ff4b4b;
                color: white;
                animation: pulse 1.5s infinite;
            }}
            
            .voice-button:disabled {{
                background-color: #f0f0f0;
                color: #ccc;
                border-color: #ccc;
                cursor: not-allowed;
            }}
            
            @keyframes pulse {{
                0% {{ opacity: 1; }}
                50% {{ opacity: 0.7; }}
                100% {{ opacity: 1; }}
            }}
            
            .mic-icon {{
                width: 16px;
                height: 16px;
                flex-shrink: 0;
            }}
            
            .status-text {{
                font-size: 12px;
                color: #666;
                margin-left: 8px;
            }}
            
            .error-text {{
                font-size: 12px;
                color: #dc3545;
                margin-left: 8px;
            }}
            
            .hidden {{
                display: none;
            }}
        </style>
    </head>
    <body>
        <div class="voice-button-container">
            <button class="voice-button" id="voiceButton">
                <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                </svg>
                <span id="buttonText">{button_text}</span>
            </button>
            <span class="status-text hidden" id="statusText">准备就绪</span>
            <span class="error-text hidden" id="errorText"></span>
        </div>

        <script>
            class VoiceInputButton {{
                constructor() {{
                    this.recognition = null;
                    this.isListening = false;
                    this.language = '{language_code}';
                    this.buttonText = '{button_text}';
                    
                    this.initializeElements();
                    this.initializeSpeechRecognition();
                    this.setupEventListeners();
                }}
                
                initializeElements() {{
                    this.voiceButton = document.getElementById('voiceButton');
                    this.buttonTextSpan = document.getElementById('buttonText');
                    this.statusText = document.getElementById('statusText');
                    this.errorText = document.getElementById('errorText');
                }}
                
                initializeSpeechRecognition() {{
                    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {{
                        this.showError('浏览器不支持语音识别');
                        this.voiceButton.disabled = true;
                        return;
                    }}
                    
                    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                    this.recognition = new SpeechRecognition();
                    
                    this.recognition.continuous = true;
                    this.recognition.interimResults = true;
                    this.recognition.maxAlternatives = 1;
                    this.recognition.lang = this.language;
                    
                    this.recognition.onstart = () => {{
                        this.isListening = true;
                        this.updateUI('listening');
                        this.hideError();
                    }};
                    
                    this.recognition.onresult = (event) => {{
                        let interimTranscript = '';
                        let finalTranscript = '';

                        for (let i = event.resultIndex; i < event.results.length; i++) {{
                            const transcript = event.results[i][0].transcript;
                            if (event.results[i].isFinal) {{
                                finalTranscript += transcript;
                            }} else {{
                                interimTranscript += transcript;
                            }}
                        }}

                        // 发送实时结果或最终结果
                        const fullTranscript = finalTranscript + interimTranscript;
                        if (fullTranscript.trim()) {{
                            this.sendResult(fullTranscript.trim(), event.results[event.results.length - 1].isFinal);
                        }}
                    }};
                    
                    this.recognition.onerror = (event) => {{
                        let errorMsg = '语音识别出错';
                        switch (event.error) {{
                            case 'no-speech':
                                errorMsg = '没有检测到语音';
                                break;
                            case 'audio-capture':
                                errorMsg = '无法访问麦克风';
                                break;
                            case 'not-allowed':
                                errorMsg = '麦克风权限被拒绝';
                                break;
                            case 'network':
                                errorMsg = '网络错误';
                                break;
                        }}
                        this.showError(errorMsg);
                        this.stopListening();
                    }};
                    
                    this.recognition.onend = () => {{
                        this.isListening = false;
                        this.updateUI('ready');
                    }};
                }}
                
                setupEventListeners() {{
                    this.voiceButton.addEventListener('click', () => {{
                        if (this.isListening) {{
                            this.stopListening();
                        }} else {{
                            this.startListening();
                        }}
                    }});
                }}
                
                startListening() {{
                    if (!this.recognition) return;
                    
                    try {{
                        this.recognition.lang = this.language;
                        this.recognition.start();
                    }} catch (error) {{
                        this.showError('启动语音识别失败');
                    }}
                }}
                
                stopListening() {{
                    if (this.recognition && this.isListening) {{
                        this.recognition.stop();
                    }}
                }}
                
                updateUI(state) {{
                    const button = this.voiceButton;
                    
                    if (state === 'listening') {{
                        button.classList.add('listening');
                        this.buttonTextSpan.textContent = '录音中...';
                        this.statusText.textContent = '正在听取语音...';
                        this.statusText.classList.remove('hidden');
                    }} else {{
                        button.classList.remove('listening');
                        this.buttonTextSpan.textContent = this.buttonText;
                        this.statusText.classList.add('hidden');
                    }}
                }}
                
                showError(message) {{
                    this.errorText.textContent = message;
                    this.errorText.classList.remove('hidden');
                    setTimeout(() => {{
                        this.errorText.classList.add('hidden');
                    }}, 3000);
                }}
                
                hideError() {{
                    this.errorText.classList.add('hidden');
                }}
                
                sendResult(transcript, isFinal = false) {{
                    // 尝试多种方式查找并更新Streamlit的文本输入框
                    try {{
                        let targetInput = null;

                        // 尝试多种选择器
                        const selectors = [
                            'input[aria-label="搜索查询"]',
                            '[data-testid="stTextInput"] input',
                            'input[placeholder*="搜索"]',
                            'input[placeholder*="输入"]',
                            '.stTextInput input'
                        ];

                        for (const selector of selectors) {{
                            targetInput = window.parent.document.querySelector(selector);
                            if (targetInput) break;
                        }}

                        if (targetInput) {{
                            // 使用多种方法设置值
                            const descriptor = Object.getOwnPropertyDescriptor(window.parent.HTMLInputElement.prototype, 'value');
                            if (descriptor && descriptor.set) {{
                                descriptor.set.call(targetInput, transcript);
                            }} else {{
                                targetInput.value = transcript;
                            }}

                            // 触发多种事件
                            const events = ['input', 'change', 'keyup', 'blur'];
                            events.forEach(eventType => {{
                                const event = new window.parent.Event(eventType, {{
                                    bubbles: true,
                                    cancelable: true
                                }});
                                targetInput.dispatchEvent(event);
                            }});

                            // React特定事件
                            if (window.parent.React) {{
                                const reactEvent = new window.parent.Event('input', {{ bubbles: true }});
                                Object.defineProperty(reactEvent, 'target', {{ value: targetInput, enumerable: true }});
                                targetInput.dispatchEvent(reactEvent);
                            }}

                            // 聚焦输入框
                            targetInput.focus();

                            console.log('成功更新输入框:', transcript);
                        }} else {{
                            console.log('未找到目标输入框，发送消息');
                            this.sendMessage(transcript, isFinal);
                        }}
                    }} catch (error) {{
                        console.error('更新输入框失败:', error);
                        this.sendMessage(transcript, isFinal);
                    }}
                }}

                sendMessage(transcript, isFinal) {{
                    // 发送消息到父窗口
                    window.parent.postMessage({{
                        type: 'voiceInput',
                        transcript: transcript,
                        isFinal: isFinal,
                        language: this.language,
                        targetKey: '{target_input_key}',
                        timestamp: Date.now()
                    }}, '*');
                }}
            }}
            
            // 初始化
            document.addEventListener('DOMContentLoaded', () => {{
                new VoiceInputButton();
            }});
        </script>
    </body>
    </html>
    """
    
    # 使用HTML组件
    component_value = components.html(
        html_code,
        height=50,
        scrolling=False
    )
    
    return component_value
