"""
语言选择器组件
提供常用语言选择和搜索功能
"""

import streamlit as st
from typing import List, Dict, Optional

def language_selector_component(
    languages: List[Dict[str, str]] = None,
    default_language: str = "zh-CN",
    show_search: bool = True,
    key: Optional[str] = None
) -> str:
    """
    语言选择器组件
    
    Args:
        languages: 语言列表
        default_language: 默认语言代码
        show_search: 是否显示搜索框
        key: 组件唯一标识
        
    Returns:
        str: 选择的语言代码
    """
    if languages is None:
        from . import get_common_languages
        languages = get_common_languages()
    
    # 创建语言映射
    language_map = {lang["name"]: lang["code"] for lang in languages}
    language_names = list(language_map.keys())
    
    # 找到默认语言的名称
    default_name = None
    for lang in languages:
        if lang["code"] == default_language:
            default_name = lang["name"]
            break
    
    if default_name is None:
        default_name = language_names[0] if language_names else ""
    
    # 搜索功能
    if show_search:
        search_query = st.text_input(
            "🔍 搜索语言",
            placeholder="输入语言名称或代码进行搜索...",
            key=f"{key}_search" if key else None
        )
        
        if search_query:
            # 过滤语言
            filtered_languages = []
            query_lower = search_query.lower()
            
            for lang in languages:
                if (query_lower in lang["name"].lower() or 
                    query_lower in lang["code"].lower()):
                    filtered_languages.append(lang)
            
            if filtered_languages:
                language_map = {lang["name"]: lang["code"] for lang in filtered_languages}
                language_names = list(language_map.keys())
                
                # 如果默认语言不在过滤结果中，选择第一个
                if default_name not in language_names:
                    default_name = language_names[0]
            else:
                st.warning("没有找到匹配的语言")
                return default_language
    
    # 语言选择
    if language_names:
        try:
            default_index = language_names.index(default_name)
        except ValueError:
            default_index = 0
        
        selected_name = st.selectbox(
            "选择语言",
            options=language_names,
            index=default_index,
            key=f"{key}_select" if key else None
        )
        
        return language_map[selected_name]
    
    return default_language

def get_language_groups():
    """获取按地区分组的语言"""
    return {
        "中文": [
            {"code": "zh-CN", "name": "中文 (普通话)"},
            {"code": "zh-TW", "name": "中文 (台灣)"},
            {"code": "zh-HK", "name": "中文 (香港)"},
        ],
        "英语": [
            {"code": "en-US", "name": "English (US)"},
            {"code": "en-GB", "name": "English (UK)"},
            {"code": "en-AU", "name": "English (Australia)"},
            {"code": "en-CA", "name": "English (Canada)"},
            {"code": "en-IN", "name": "English (India)"},
        ],
        "西班牙语": [
            {"code": "es-ES", "name": "Español (España)"},
            {"code": "es-MX", "name": "Español (México)"},
            {"code": "es-AR", "name": "Español (Argentina)"},
            {"code": "es-CO", "name": "Español (Colombia)"},
            {"code": "es-CL", "name": "Español (Chile)"},
            {"code": "es-PE", "name": "Español (Perú)"},
        ],
        "欧洲语言": [
            {"code": "fr-FR", "name": "Français (France)"},
            {"code": "de-DE", "name": "Deutsch"},
            {"code": "it-IT", "name": "Italiano"},
            {"code": "pt-PT", "name": "Português (Portugal)"},
            {"code": "pt-BR", "name": "Português (Brasil)"},
            {"code": "ru-RU", "name": "Русский"},
            {"code": "nl-NL", "name": "Nederlands"},
            {"code": "sv-SE", "name": "Svenska"},
            {"code": "da-DK", "name": "Dansk"},
            {"code": "no-NO", "name": "Norsk"},
        ],
        "亚洲语言": [
            {"code": "ja-JP", "name": "日本語"},
            {"code": "ko-KR", "name": "한국어"},
            {"code": "hi-IN", "name": "हिन्दी"},
            {"code": "th-TH", "name": "ไทย"},
            {"code": "vi-VN", "name": "Tiếng Việt"},
            {"code": "id-ID", "name": "Bahasa Indonesia"},
            {"code": "ms-MY", "name": "Bahasa Malaysia"},
        ],
        "其他语言": [
            {"code": "ar-SA", "name": "العربية"},
            {"code": "he-IL", "name": "עברית"},
            {"code": "tr-TR", "name": "Türkçe"},
            {"code": "pl-PL", "name": "Polski"},
            {"code": "cs-CZ", "name": "Čeština"},
            {"code": "hu-HU", "name": "Magyar"},
            {"code": "ro-RO", "name": "Română"},
        ]
    }

def grouped_language_selector(
    default_language: str = "zh-CN",
    key: Optional[str] = None
) -> str:
    """
    分组语言选择器
    
    Args:
        default_language: 默认语言代码
        key: 组件唯一标识
        
    Returns:
        str: 选择的语言代码
    """
    language_groups = get_language_groups()
    
    # 创建所有语言的平面列表
    all_languages = []
    for group_name, languages in language_groups.items():
        all_languages.extend(languages)
    
    # 搜索功能
    search_query = st.text_input(
        "🔍 搜索语言",
        placeholder="输入语言名称或代码进行搜索...",
        key=f"{key}_search" if key else None
    )
    
    if search_query:
        # 搜索模式
        filtered_languages = []
        query_lower = search_query.lower()
        
        for lang in all_languages:
            if (query_lower in lang["name"].lower() or 
                query_lower in lang["code"].lower()):
                filtered_languages.append(lang)
        
        if filtered_languages:
            language_map = {f"{lang['name']} ({lang['code']})": lang["code"] 
                          for lang in filtered_languages}
            language_options = list(language_map.keys())
            
            # 找到默认选项
            default_option = None
            for option, code in language_map.items():
                if code == default_language:
                    default_option = option
                    break
            
            if default_option is None:
                default_option = language_options[0] if language_options else ""
            
            try:
                default_index = language_options.index(default_option)
            except ValueError:
                default_index = 0
            
            selected_option = st.selectbox(
                "选择语言",
                options=language_options,
                index=default_index,
                key=f"{key}_select" if key else None
            )
            
            return language_map[selected_option]
        else:
            st.warning("没有找到匹配的语言")
            return default_language
    else:
        # 分组显示模式
        st.markdown("### 选择语言")
        
        # 创建标签页
        group_names = list(language_groups.keys())
        tabs = st.tabs(group_names)
        
        selected_language = default_language
        
        for i, (group_name, languages) in enumerate(language_groups.items()):
            with tabs[i]:
                language_map = {lang["name"]: lang["code"] for lang in languages}
                language_names = list(language_map.keys())
                
                # 检查默认语言是否在当前组
                default_name = None
                for lang in languages:
                    if lang["code"] == default_language:
                        default_name = lang["name"]
                        break
                
                if default_name:
                    try:
                        default_index = language_names.index(default_name)
                    except ValueError:
                        default_index = 0
                else:
                    default_index = 0
                
                selected_name = st.selectbox(
                    f"选择{group_name}",
                    options=language_names,
                    index=default_index,
                    key=f"{key}_{group_name}" if key else None
                )
                
                if selected_name:
                    selected_language = language_map[selected_name]
        
        return selected_language
