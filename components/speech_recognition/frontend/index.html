<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speech Recognition Component</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #ffffff;
        }
        
        .speech-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .language-selector {
            margin-bottom: 20px;
        }
        
        .language-search {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .language-dropdown {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }
        
        .speech-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .speech-button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .speech-button.start {
            background-color: #ff4b4b;
            color: white;
        }
        
        .speech-button.start:hover {
            background-color: #ff3333;
        }
        
        .speech-button.listening {
            background-color: #ff6b6b;
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .speech-button.stop {
            background-color: #666;
            color: white;
        }
        
        .speech-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .status-indicator {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-ready {
            background-color: #e8f5e8;
            color: #2d5a2d;
        }
        
        .status-listening {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .result-area {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 60px;
            background-color: #f8f9fa;
            font-size: 16px;
            line-height: 1.5;
        }
        
        .result-placeholder {
            color: #6c757d;
            font-style: italic;
        }
        
        .interim-result {
            color: #6c757d;
            font-style: italic;
        }
        
        .final-result {
            color: #212529;
            font-weight: 500;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .mic-icon {
            width: 20px;
            height: 20px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="speech-container">
        <div class="language-selector">
            <input type="text" class="language-search" placeholder="搜索语言..." id="languageSearch">
            <select class="language-dropdown" id="languageSelect">
                <!-- 语言选项将通过JavaScript动态添加 -->
            </select>
        </div>
        
        <div class="speech-controls">
            <button class="speech-button start" id="speechButton">
                <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                </svg>
                开始录音
            </button>
            
            <div class="status-indicator status-ready" id="statusIndicator">
                准备就绪
            </div>
        </div>
        
        <div class="result-area" id="resultArea">
            <div class="result-placeholder">点击开始录音按钮开始语音识别...</div>
        </div>
        
        <div class="error-message hidden" id="errorMessage"></div>
    </div>

    <script src="index.js"></script>
</body>
</html>
