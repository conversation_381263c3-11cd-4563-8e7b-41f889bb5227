// 语音识别组件的JavaScript实现
class SpeechRecognitionComponent {
    constructor() {
        this.recognition = null;
        this.isListening = false;
        this.languages = [];
        this.selectedLanguage = 'zh-CN';
        this.finalTranscript = '';
        this.interimTranscript = '';
        
        this.initializeElements();
        this.initializeSpeechRecognition();
        this.setupEventListeners();
        
        // 监听来自Streamlit的参数
        window.addEventListener('message', this.handleStreamlitMessage.bind(this));
    }
    
    initializeElements() {
        this.languageSearch = document.getElementById('languageSearch');
        this.languageSelect = document.getElementById('languageSelect');
        this.speechButton = document.getElementById('speechButton');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.resultArea = document.getElementById('resultArea');
        this.errorMessage = document.getElementById('errorMessage');
    }
    
    initializeSpeechRecognition() {
        // 检查浏览器支持
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showError('您的浏览器不支持语音识别功能。请使用Chrome、Edge或Safari浏览器。');
            this.speechButton.disabled = true;
            return;
        }
        
        // 创建语音识别实例
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        // 配置语音识别
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.maxAlternatives = 1;
        
        // 设置事件监听器
        this.recognition.onstart = () => {
            this.isListening = true;
            this.updateUI('listening');
            this.hideError();
        };
        
        this.recognition.onresult = (event) => {
            let interimTranscript = '';
            let finalTranscript = this.finalTranscript;
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }
            
            this.finalTranscript = finalTranscript;
            this.interimTranscript = interimTranscript;
            this.updateResult();
            
            // 发送结果到Streamlit
            this.sendToStreamlit({
                transcript: finalTranscript + interimTranscript,
                isFinal: interimTranscript === '',
                language: this.selectedLanguage
            });
        };
        
        this.recognition.onerror = (event) => {
            console.error('语音识别错误:', event.error);
            let errorMsg = '语音识别出错';
            
            switch (event.error) {
                case 'no-speech':
                    errorMsg = '没有检测到语音，请重试';
                    break;
                case 'audio-capture':
                    errorMsg = '无法访问麦克风，请检查权限设置';
                    break;
                case 'not-allowed':
                    errorMsg = '麦克风权限被拒绝，请允许访问麦克风';
                    break;
                case 'network':
                    errorMsg = '网络错误，请检查网络连接';
                    break;
                case 'language-not-supported':
                    errorMsg = '不支持所选语言';
                    break;
            }
            
            this.showError(errorMsg);
            this.stopListening();
        };
        
        this.recognition.onend = () => {
            this.isListening = false;
            this.updateUI('ready');
        };
    }
    
    setupEventListeners() {
        // 语音按钮点击事件
        this.speechButton.addEventListener('click', () => {
            if (this.isListening) {
                this.stopListening();
            } else {
                this.startListening();
            }
        });
        
        // 语言选择事件
        this.languageSelect.addEventListener('change', (e) => {
            this.selectedLanguage = e.target.value;
            if (this.recognition) {
                this.recognition.lang = this.selectedLanguage;
            }
        });
        
        // 语言搜索事件
        this.languageSearch.addEventListener('input', (e) => {
            this.filterLanguages(e.target.value);
        });
    }
    
    handleStreamlitMessage(event) {
        const data = event.data;
        if (data.type === 'streamlit:componentReady') {
            // 组件准备就绪
            this.sendToStreamlit({ ready: true });
        } else if (data.type === 'streamlit:render') {
            // 接收来自Streamlit的参数
            const args = data.args;
            if (args.languages) {
                this.languages = args.languages;
                this.populateLanguageSelect();
            }
            if (args.selectedLanguage) {
                this.selectedLanguage = args.selectedLanguage;
                this.languageSelect.value = this.selectedLanguage;
                if (this.recognition) {
                    this.recognition.lang = this.selectedLanguage;
                }
            }
        }
    }
    
    populateLanguageSelect() {
        this.languageSelect.innerHTML = '';
        this.languages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang.code;
            option.textContent = lang.name;
            this.languageSelect.appendChild(option);
        });
        
        // 设置默认选择
        this.languageSelect.value = this.selectedLanguage;
    }
    
    filterLanguages(query) {
        const filteredLanguages = this.languages.filter(lang => 
            lang.name.toLowerCase().includes(query.toLowerCase()) ||
            lang.code.toLowerCase().includes(query.toLowerCase())
        );
        
        this.languageSelect.innerHTML = '';
        filteredLanguages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang.code;
            option.textContent = lang.name;
            this.languageSelect.appendChild(option);
        });
        
        if (filteredLanguages.length > 0) {
            this.languageSelect.value = filteredLanguages[0].code;
            this.selectedLanguage = filteredLanguages[0].code;
            if (this.recognition) {
                this.recognition.lang = this.selectedLanguage;
            }
        }
    }
    
    startListening() {
        if (!this.recognition) {
            this.showError('语音识别未初始化');
            return;
        }
        
        this.finalTranscript = '';
        this.interimTranscript = '';
        this.recognition.lang = this.selectedLanguage;
        
        try {
            this.recognition.start();
            this.updateUI('listening');
        } catch (error) {
            console.error('启动语音识别失败:', error);
            this.showError('启动语音识别失败，请重试');
        }
    }
    
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
        this.updateUI('ready');
    }
    
    updateUI(state) {
        const button = this.speechButton;
        const status = this.statusIndicator;
        
        // 重置所有状态类
        button.className = 'speech-button';
        status.className = 'status-indicator';
        
        switch (state) {
            case 'ready':
                button.classList.add('start');
                button.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                    开始录音
                `;
                status.classList.add('status-ready');
                status.textContent = '准备就绪';
                break;
                
            case 'listening':
                button.classList.add('listening');
                button.innerHTML = `
                    <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                    正在录音...
                `;
                status.classList.add('status-listening');
                status.textContent = '正在听取语音...';
                break;
        }
    }
    
    updateResult() {
        const resultArea = this.resultArea;
        
        if (this.finalTranscript || this.interimTranscript) {
            resultArea.innerHTML = '';
            
            if (this.finalTranscript) {
                const finalSpan = document.createElement('span');
                finalSpan.className = 'final-result';
                finalSpan.textContent = this.finalTranscript;
                resultArea.appendChild(finalSpan);
            }
            
            if (this.interimTranscript) {
                const interimSpan = document.createElement('span');
                interimSpan.className = 'interim-result';
                interimSpan.textContent = this.interimTranscript;
                resultArea.appendChild(interimSpan);
            }
        } else {
            resultArea.innerHTML = '<div class="result-placeholder">点击开始录音按钮开始语音识别...</div>';
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.classList.remove('hidden');
        
        const status = this.statusIndicator;
        status.className = 'status-indicator status-error';
        status.textContent = '出现错误';
    }
    
    hideError() {
        this.errorMessage.classList.add('hidden');
    }
    
    sendToStreamlit(data) {
        window.parent.postMessage({
            type: 'streamlit:componentValue',
            value: data
        }, '*');
    }
}

// 初始化组件
document.addEventListener('DOMContentLoaded', () => {
    new SpeechRecognitionComponent();
});
