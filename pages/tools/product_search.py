"""
智能商品搜索页面
支持多语言语义搜索
"""

import streamlit as st
from utils.semantic_search_engine import SemanticSearchEngine
from utils.product_data_manager import ProductDataManager
from components.speech_recognition.voice_button import voice_input_button, get_button_text

# 获取认证器
authenticator = st.session_state.get('authenticator')
try:
    # 使用 'unrendered' 位置来隐藏登录组件，但保持功能
    authenticator.login(location='unrendered')
except Exception as e:
    st.error(f"认证错误: {e}")

# 页面配置
st.set_page_config(
    page_title="Product Search / Búsqueda de Productos",
    page_icon="🔍",
    layout="wide"
)

# 使用缓存资源装饰器
@st.cache_resource
def get_search_engine():
    """获取搜索引擎"""
    return SemanticSearchEngine()

@st.cache_resource
def get_product_manager():
    """获取商品数据管理器"""
    return ProductDataManager()

def main():
    """主函数"""
    st.title("🔍 Product Location Search / Búsqueda de Ubicación de Productos")
    st.markdown("Semantic search supporting multi languages / Búsqueda semántica compatible con ")
    st.markdown("---")
    
    # 初始化搜索引擎
    search_engine = get_search_engine()
    product_manager = get_product_manager()
    
    # 检查索引状态
    index_info = search_engine.get_index_info()
    
    if not index_info['index_exists']:
        st.warning("⚠️ Search index does not exist. Please build the index first in the product management page / El índice de búsqueda no existe. Por favor, construya el índice primero en la página de gestión de productos")

        if st.button("🔄 Build Index Now / Construir Índice Ahora"):
            with st.spinner("Building index, please wait... / Construyendo índice, por favor espere..."):
                try:
                    success = search_engine.build_index(force_rebuild=True)
                    if success:
                        st.success("Index built successfully! / ¡Índice construido exitosamente!")
                        st.rerun()
                    else:
                        st.error("Failed to build index / Error al construir el índice")
                except Exception as e:
                    st.error(f"Error building index / Error al construir el índice: {str(e)}")
        return
    
    # 显示搜索界面
    show_search_interface(search_engine, product_manager)

def show_search_interface(search_engine, product_manager):
    """显示搜索界面"""

    st.markdown("### 🔍 Search Products / Buscar Productos")

    # 语言选择（用于语音识别）
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("**Voice Recognition Language / Idioma de Reconocimiento de Voz**")

    with col2:
        # 语言选择下拉框 - 只保留西班牙语和英语
        language_options = [
            ("es-ES", "Español (España)"),
            ("es-MX", "Español (México)"),
            ("en-US", "English (US)"),
            ("en-GB", "English (UK)"),
        ]

        selected_language = st.selectbox(
            "Voice Recognition Language",
            options=language_options,
            format_func=lambda x: x[1],
            index=0,  # 默认选择西班牙语
            key="voice_language_select",
            label_visibility="collapsed"
        )[0]

    st.markdown("---")

    # 搜索框和语音按钮
    col1, col2, col3 = st.columns([4, 1, 1])

    with col1:
        # 初始化搜索查询
        if 'search_query' not in st.session_state:
            st.session_state.search_query = ""

        query = st.text_input(
            "Search Query",
            value=st.session_state.search_query,
            placeholder="Buscar productos / Search products...",
            help="Ejemplos: agua, cuchillo, knife, water, cutting tool",
            label_visibility="collapsed",
            key="search_input"
        )

    with col2:
        # 语音输入按钮
        st.markdown("**Voice / Voz**")
        voice_result = voice_input_button(
            language_code=selected_language,
            key="voice_input_btn",
            target_input_key="search_input"
        )

    with col3:
        st.markdown("**Search / Buscar**")
        search_button = st.button("Search / Buscar", type="primary", use_container_width=True, key="search_btn")

    # 搜索结果数量选择
    result_count = st.slider("Number of Results / Número de Resultados", min_value=1, max_value=10, value=5)
    
    # 更新session state中的查询
    if query != st.session_state.search_query:
        st.session_state.search_query = query

    # 执行搜索
    if query and query.strip() and (search_button or query != st.session_state.get('last_query', '')):
        st.session_state.last_query = query.strip()

        with st.spinner("正在搜索..."):
            try:
                results = search_engine.search(query.strip(), top_k=result_count)
                display_search_results(results, query.strip())
            except Exception as e:
                st.error(f"Search failed / Búsqueda fallida: {str(e)}")
                st.info("💡 Tip: If search fails, try rebuilding the index / Consejo: Si la búsqueda falla, intente reconstruir el índice")
    
    # 不显示搜索示例

def display_search_results(results, query):
    """显示搜索结果"""

    if not results:
        st.warning(f"No products found related to '{query}' / No se encontraron productos relacionados con '{query}'")
        return

    st.markdown(f"### 🎯 Search Results / Resultados de Búsqueda ({len(results)} products found / productos encontrados)")
    st.markdown("---")
    
    for i, (product, score) in enumerate(results, 1):
        # 创建结果卡片
        with st.container():
            col1, col2, col3 = st.columns([0.5, 2, 1])
            
            with col1:
                # 相似度分数
                score_color = "green" if score > 0.7 else "orange" if score > 0.5 else "red"
                st.markdown(f"**#{i}**")
                st.markdown(f"<span style='color: {score_color}'>Match: {score:.2f}</span>",
                           unsafe_allow_html=True)

            with col2:
                # 商品信息 - 优先显示西班牙语，然后英语
                if product.get('product_name_es'):
                    st.markdown(f"**🏷️ {product['product_name_es']}**")
                    if product.get('product_name_zh'):
                        st.markdown(f"*{product['product_name_zh']}*")
                else:
                    st.markdown(f"**🏷️ {product.get('product_name_zh', 'Unknown Product')}**")

                # 位置信息（突出显示）
                st.markdown(f"📍 **Location / Ubicación**: {product['location']}")

                # 关键词
                if product.get('keywords'):
                    keywords = product['keywords'].split(',')
                    keyword_html = " ".join([f"<span style='background-color: #f0f2f6; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;'>{keyword.strip()}</span>" for keyword in keywords[:5]])
                    st.markdown(f"🔖 {keyword_html}", unsafe_allow_html=True)
            
            with col3:
                # 商品ID
                st.markdown(f"**ID**: {product['product_id']}")
        
        st.markdown("---")

def show_search_examples():
    """显示搜索示例"""
    
    st.markdown("### 💡 搜索示例")
    
    # 创建示例按钮
    examples = [
        ("水", "中文搜索"),
        ("agua", "西班牙语搜索"),
        ("water", "英语搜索"),
        ("用来切菜的东西", "语义搜索"),
        ("cuchillo", "西班牙语工具"),
        ("洗头发的", "中文描述"),
        ("fruta", "西班牙语类别"),
        ("something to drink", "英语描述")
    ]
    
    st.markdown("点击下面的示例来体验多语言搜索：")
    
    # 创建按钮网格
    cols = st.columns(4)
    for i, (example, description) in enumerate(examples):
        with cols[i % 4]:
            if st.button(f"{example}", help=description, key=f"example_{i}"):
                # 设置查询并触发搜索
                st.session_state.search_query = example
                st.rerun()
    
    # 处理示例搜索
    if 'search_query' in st.session_state:
        query = st.session_state.search_query
        del st.session_state.search_query
        
        with st.spinner(f"正在搜索 '{query}'..."):
            try:
                search_engine = get_search_engine()
                results = search_engine.search(query, top_k=5)
                
                st.markdown(f"### 示例搜索结果: '{query}'")
                display_search_results(results, query)
            except Exception as e:
                st.error(f"示例搜索失败: {str(e)}")
    
    # 使用说明
    st.markdown("---")
    st.markdown("### 📖 使用说明")
    
    with st.expander("如何使用智能搜索？"):
        st.markdown("""
        **多语言支持**:
        - 🇨🇳 中文：水、苹果、菜刀、洗发水
        - 🇪🇸 西班牙语：agua、manzana、cuchillo、champú
        - 🇬🇧 英语：water、apple、knife、shampoo
        
        **语义搜索**:
        - 不需要精确匹配商品名称
        - 可以用描述性语言搜索，如"用来切菜的东西"
        - 系统会理解语义并找到相关商品
        
        **搜索技巧**:
        - 使用简单明确的词语
        - 可以混合使用不同语言
        - 尝试用功能或用途来描述商品
        """)
    
    with st.expander("相似度分数说明"):
        st.markdown("""
        - 🟢 **0.7-1.0**: 高度相关，很可能是您要找的商品
        - 🟡 **0.5-0.7**: 中等相关，可能相关
        - 🔴 **0.0-0.5**: 低相关性，可能不是您要找的
        
        相似度分数基于语义相似性计算，分数越高表示与搜索查询越相关。
        """)

if __name__ == "__main__":
    main()
