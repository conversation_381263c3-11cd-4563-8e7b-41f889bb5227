"""
商品管理页面
用于商品信息的增删查改
"""

import streamlit as st
import pandas as pd
from utils.global_resources import get_global_product_manager, get_global_search_engine, clear_global_caches

# 页面配置
st.set_page_config(
    page_title="商品管理",
    page_icon="📦",
    layout="wide"
)

def main():
    """主函数"""
    st.title("📦 商品管理系统")
    st.markdown("---")
    
    # 初始化管理器（使用全局资源）
    product_manager = get_global_product_manager()
    search_engine = get_global_search_engine()
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📋 商品列表", "➕ 添加商品", "✏️ 编辑商品", "🗑️ 删除商品", "🔧 索引管理"])

    with tab1:
        show_product_list(product_manager)

    with tab2:
        add_product_form(product_manager, search_engine)

    with tab3:
        edit_product_form(product_manager, search_engine)

    with tab4:
        delete_product_form(product_manager, search_engine)

    with tab5:
        manage_search_index(search_engine)

def show_product_list(product_manager):
    """显示商品列表"""
    st.header("商品列表")

    # 加载商品数据
    df = product_manager.load_products()

    if len(df) == 0:
        st.info("暂无商品数据")
        return

    # 显示统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("商品总数", len(df))
    with col2:
        # 计算有西班牙语名称的商品数量
        has_spanish = df['product_name_es'].notna() & (df['product_name_es'] != '')
        st.metric("有西班牙语名称", has_spanish.sum())
    with col3:
        # 计算有关键词的商品数量
        if 'keywords' in df.columns:
            has_keywords = df['keywords'].notna() & (df['keywords'] != '')
            st.metric("有关键词", has_keywords.sum())
        else:
            st.metric("有关键词", 0)

    st.markdown("---")

    # 搜索和分页设置
    col1, col2 = st.columns([3, 1])

    with col1:
        search_term = st.text_input("🔍 搜索商品", placeholder="输入商品名称、位置或标签进行搜索...")

    with col2:
        page_size = st.selectbox(
            "每页显示",
            options=[10, 20, 50, 100],
            index=1,  # 默认20条
            help="选择每页显示的商品数量"
        )

    # 过滤数据
    if search_term:
        mask = (
            df['product_name_zh'].str.contains(search_term, case=False, na=False) |
            df['product_name_es'].str.contains(search_term, case=False, na=False) |
            df['location'].str.contains(search_term, case=False, na=False)
        )

        # 如果keywords列存在，也加入搜索
        if 'keywords' in df.columns:
            mask = mask | df['keywords'].str.contains(search_term, case=False, na=False)
        filtered_df = df[mask]
    else:
        filtered_df = df

    # 分页逻辑
    total_items = len(filtered_df)

    if total_items == 0:
        st.warning("没有找到匹配的商品")
        return

    # 计算总页数
    total_pages = (total_items - 1) // page_size + 1

    # 分页控件
    if total_pages > 1:
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("⬅️ 上一页", disabled=st.session_state.get('current_page', 1) <= 1):
                st.session_state.current_page = max(1, st.session_state.get('current_page', 1) - 1)
                st.rerun()

        with col2:
            # 页码选择
            current_page = st.selectbox(
                f"页码 (共 {total_pages} 页)",
                options=list(range(1, total_pages + 1)),
                index=st.session_state.get('current_page', 1) - 1,
                key="page_selector"
            )

            if current_page != st.session_state.get('current_page', 1):
                st.session_state.current_page = current_page
                st.rerun()

        with col3:
            if st.button("➡️ 下一页", disabled=st.session_state.get('current_page', 1) >= total_pages):
                st.session_state.current_page = min(total_pages, st.session_state.get('current_page', 1) + 1)
                st.rerun()

    # 获取当前页数据
    current_page = st.session_state.get('current_page', 1)
    start_idx = (current_page - 1) * page_size
    end_idx = min(start_idx + page_size, total_items)

    page_df = filtered_df.iloc[start_idx:end_idx]

    # 显示当前页信息
    st.info(f"显示第 {start_idx + 1}-{end_idx} 条，共 {total_items} 条商品")

    # 显示商品表格
    if len(page_df) > 0:
        # 重新排列列的顺序，使其更易读
        columns_to_show = ['product_id', 'product_name_zh', 'product_name_es', 'location']
        column_names = ['ID', '中文名称', '西班牙语名称', '位置']

        # 检查keywords列是否存在并且在当前页数据中存在
        if 'keywords' in page_df.columns and not page_df['keywords'].isna().all():
            columns_to_show.append('keywords')
            column_names.append('关键词')

        # 只选择存在的列
        available_columns = [col for col in columns_to_show if col in page_df.columns]
        available_names = [column_names[i] for i, col in enumerate(columns_to_show) if col in page_df.columns]

        display_df = page_df[available_columns].copy()
        display_df.columns = available_names

        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True
        )

def add_product_form(product_manager, search_engine):
    """添加商品表单"""
    st.header("添加新商品")
    
    with st.form("add_product_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            product_name_zh = st.text_input(
                "中文名称 *", 
                placeholder="请输入商品的中文名称",
                help="必填字段"
            )
            
            location = st.text_input(
                "商品位置 *", 
                placeholder="例如：过道1，货架A",
                help="必填字段，描述商品在店内的具体位置"
            )
        
        with col2:
            product_name_es = st.text_input(
                "西班牙语名称", 
                placeholder="请输入商品的西班牙语名称",
                help="选填字段"
            )
            
        # 关键词字段（跨两列）
        keywords = st.text_area(
            "关键词",
            placeholder="输入所有相关的关键词，包含分类、品牌、描述性词汇等，用逗号分隔",
            help="万能关键词字段，包含：分类、品牌、标签、多语言词汇等。例如：饮料,水,饮品,bebida,agua,喝水,解渴,饮用水,mineral water",
            height=100
        )
        
        submitted = st.form_submit_button("添加商品", type="primary")
        
        if submitted:
            # 验证必填字段
            if not product_name_zh.strip():
                st.error("请输入中文名称")
                return
            
            if not location.strip():
                st.error("请输入商品位置")
                return
            
            # 准备商品数据
            product_data = {
                'product_name_zh': product_name_zh.strip(),
                'location': location.strip()
            }
            
            if product_name_es.strip():
                product_data['product_name_es'] = product_name_es.strip()

            if keywords.strip():
                product_data['keywords'] = keywords.strip()
            
            # 添加商品
            success, message = product_manager.add_product(product_data)
            
            if success:
                st.success(message)
                st.info("💡 提示：添加新商品后，建议重建搜索索引以获得最佳搜索效果")
                
                # 清除缓存以刷新数据
                clear_global_caches()
                
                # 自动跳转到商品列表
                st.rerun()
            else:
                st.error(message)

def delete_product_form(product_manager, search_engine):
    """删除商品表单"""
    st.header("删除商品")

    # 加载商品数据
    df = product_manager.load_products()

    if len(df) == 0:
        st.info("暂无商品数据")
        return

    st.markdown("### 选择要删除的商品")
    st.markdown("您可以通过以下两种方式选择要删除的商品：")

    # 创建两个标签页：搜索选择和直接输入ID
    tab1, tab2 = st.tabs(["🔍 搜索选择", "🔢 直接输入ID"])

    selected_id = None

    with tab1:
        st.markdown("**通过搜索选择商品**")

        # 搜索框
        search_term = st.text_input(
            "搜索商品",
            placeholder="输入商品名称、位置或关键词进行搜索...",
            key="delete_search"
        )

        # 过滤商品
        if search_term:
            mask = (
                df['product_id'].astype(str).str.contains(search_term, case=False, na=False) |
                df['product_name_zh'].str.contains(search_term, case=False, na=False) |
                df['product_name_es'].str.contains(search_term, case=False, na=False) |
                df['location'].str.contains(search_term, case=False, na=False)
            )

            # 如果keywords列存在，也加入搜索
            if 'keywords' in df.columns:
                mask = mask | df['keywords'].str.contains(search_term, case=False, na=False)

            filtered_df = df[mask]

            if len(filtered_df) > 0:
                st.success(f"找到 {len(filtered_df)} 个匹配的商品")

                # 显示搜索结果供选择
                product_options = []
                for _, row in filtered_df.iterrows():
                    option_text = f"ID:{row['product_id']} - {row['product_name_zh']} ({row['location']})"
                    product_options.append((option_text, int(row['product_id'])))

                if product_options:
                    selected_option = st.selectbox(
                        "从搜索结果中选择商品",
                        options=["请选择..."] + [opt[0] for opt in product_options],
                        key="delete_select"
                    )

                    if selected_option != "请选择...":
                        selected_id = next(opt[1] for opt in product_options if opt[0] == selected_option)
            else:
                st.warning("没有找到匹配的商品")
        else:
            st.info("请输入搜索关键词来查找商品")

    with tab2:
        st.markdown("**直接输入商品ID**")

        # ID输入框
        input_id = st.number_input(
            "商品ID",
            min_value=1,
            step=1,
            value=None,
            placeholder="请输入商品ID",
            help="输入要删除的商品的ID号",
            key="delete_id_input"
        )

        if input_id:
            # 检查ID是否存在
            if input_id in df['product_id'].astype(int).values:
                selected_id = int(input_id)
                st.success(f"找到商品ID: {input_id}")
            else:
                st.error(f"商品ID {input_id} 不存在")

    # 显示选中的商品详情和删除确认
    if selected_id:
        product = product_manager.get_product_by_id(selected_id)
        if product:
            st.markdown("---")
            st.markdown("### 📋 商品详情")

            # 使用卡片样式显示商品信息
            with st.container():
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown(f"**🆔 商品ID:** {product['product_id']}")
                    st.markdown(f"**🏷️ 中文名称:** {product['product_name_zh']}")
                    st.markdown(f"**📍 位置:** {product['location']}")

                with col2:
                    spanish_name = product.get('product_name_es', '')
                    st.markdown(f"**🇪🇸 西班牙语名称:** {spanish_name if spanish_name else '无'}")

                # 关键词显示在单独的行
                keywords = product.get('keywords', '')
                if keywords:
                    st.markdown(f"**🔖 关键词:** {keywords}")

            # 删除确认
            st.markdown("---")
            st.error("⚠️ **危险操作：删除商品**")
            st.markdown("删除操作不可撤销，请仔细确认要删除的商品信息是否正确。")

            # 二次确认
            confirm_delete = st.checkbox(
                f"我确认要删除商品：{product['product_name_zh']} (ID: {product['product_id']})",
                key="confirm_delete_checkbox"
            )

            col1, col2, col3 = st.columns([1, 1, 2])

            with col1:
                if st.button(
                    "🗑️ 确认删除",
                    type="primary",
                    disabled=not confirm_delete,
                    help="请先勾选确认框"
                ):
                    success, message = product_manager.delete_product(selected_id)

                    if success:
                        st.success(message)
                        st.info("💡 提示：删除商品后，建议重建搜索索引")

                        # 清除缓存以刷新数据
                        clear_global_caches()

                        # 清除session state
                        if 'confirm_delete_checkbox' in st.session_state:
                            del st.session_state.confirm_delete_checkbox

                        # 刷新页面
                        st.rerun()
                    else:
                        st.error(message)

            with col2:
                if st.button("❌ 取消", help="取消删除操作"):
                    # 清除选择状态
                    if 'confirm_delete_checkbox' in st.session_state:
                        del st.session_state.confirm_delete_checkbox
                    st.rerun()

def edit_product_form(product_manager, search_engine):
    """编辑商品表单"""
    st.header("编辑商品")

    # 加载商品数据
    df = product_manager.load_products()

    if len(df) == 0:
        st.info("暂无商品数据")
        return

    # 选择要编辑的商品
    st.markdown("### 选择要编辑的商品")

    # 搜索框
    search_term = st.text_input("🔍 搜索商品", placeholder="输入商品名称、ID或位置进行搜索...")

    # 过滤商品
    if search_term:
        mask = (
            df['product_id'].astype(str).str.contains(search_term, case=False, na=False) |
            df['product_name_zh'].str.contains(search_term, case=False, na=False) |
            df['product_name_es'].str.contains(search_term, case=False, na=False) |
            df['location'].str.contains(search_term, case=False, na=False)
        )
        filtered_df = df[mask]
    else:
        filtered_df = df

    if len(filtered_df) == 0:
        st.warning("没有找到匹配的商品")
        return

    # 创建商品选择选项
    product_options = []
    for _, row in filtered_df.iterrows():
        option_text = f"ID:{row['product_id']} - {row['product_name_zh']} ({row['location']})"
        product_options.append((option_text, int(row['product_id'])))

    # 选择商品
    selected_option = st.selectbox(
        "选择要编辑的商品",
        options=[opt[0] for opt in product_options],
        help="从搜索结果中选择一个商品进行编辑"
    )

    if not selected_option:
        return

    # 获取选中的商品ID和数据
    selected_id = next(opt[1] for opt in product_options if opt[0] == selected_option)
    current_product = product_manager.get_product_by_id(selected_id)

    if not current_product:
        st.error("无法获取商品信息")
        return

    st.markdown("---")
    st.markdown("### 编辑商品信息")

    # 编辑表单
    with st.form("edit_product_form"):
        col1, col2 = st.columns(2)

        with col1:
            product_name_zh = st.text_input(
                "中文名称 *",
                value=current_product.get('product_name_zh', ''),
                placeholder="请输入商品的中文名称",
                help="必填字段"
            )

            location = st.text_input(
                "商品位置 *",
                value=current_product.get('location', ''),
                placeholder="例如：过道1，货架A",
                help="必填字段，描述商品在店内的具体位置"
            )

        with col2:
            product_name_es = st.text_input(
                "西班牙语名称",
                value=current_product.get('product_name_es', ''),
                placeholder="请输入商品的西班牙语名称",
                help="选填字段"
            )

        # 关键词字段（跨两列）
        keywords = st.text_area(
            "关键词",
            value=current_product.get('keywords', ''),
            placeholder="输入所有相关的关键词，包含分类、品牌、描述性词汇等，用逗号分隔",
            help="万能关键词字段，包含：分类、品牌、标签、多语言词汇等。例如：饮料,水,饮品,bebida,agua,喝水,解渴,饮用水,mineral water",
            height=100
        )

        submitted = st.form_submit_button("保存修改", type="primary")

        if submitted:
            # 验证必填字段
            if not product_name_zh.strip():
                st.error("请输入中文名称")
                return

            if not location.strip():
                st.error("请输入商品位置")
                return

            # 准备更新数据
            update_data = {
                'product_name_zh': product_name_zh.strip(),
                'location': location.strip(),
                'product_name_es': product_name_es.strip(),
                'keywords': keywords.strip()
            }

            # 更新商品
            success, message = product_manager.update_product(selected_id, update_data)

            if success:
                st.success(message)
                st.info("💡 提示：修改商品后，建议重建搜索索引以获得最佳搜索效果")

                # 清除缓存以刷新数据
                clear_global_caches()

                # 刷新页面
                st.rerun()
            else:
                st.error(message)

def manage_search_index(search_engine):
    """管理搜索索引"""
    st.header("搜索索引管理")
    
    # 获取索引信息
    index_info = search_engine.get_index_info()
    
    # 显示索引状态
    st.markdown("### 索引状态")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status = "✅ 存在" if index_info['index_exists'] else "❌ 不存在"
        st.metric("索引状态", status)
    
    with col2:
        st.metric("商品数量", index_info['product_count'])
    
    with col3:
        st.metric("向量维度", index_info['index_dimension'])
    
    st.markdown("---")
    
    # 索引操作
    st.markdown("### 索引操作")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔄 重建索引", type="primary"):
            with st.spinner("正在重建索引，请稍候..."):
                try:
                    success = search_engine.build_index(force_rebuild=True)
                    if success:
                        st.success("索引重建成功！")
                        # 清除缓存
                        clear_global_caches()
                        st.rerun()
                    else:
                        st.error("索引重建失败，请检查日志")
                except Exception as e:
                    st.error(f"索引重建失败: {str(e)}")
    
    with col2:
        if st.button("📊 加载现有索引"):
            with st.spinner("正在加载索引..."):
                try:
                    success = search_engine.load_index()
                    if success:
                        st.success("索引加载成功！")
                        st.rerun()
                    else:
                        st.warning("没有找到现有索引，请先构建索引")
                except Exception as e:
                    st.error(f"索引加载失败: {str(e)}")
    
    # 使用说明
    st.markdown("---")
    st.markdown("### 使用说明")
    st.info("""
    - **重建索引**: 根据当前商品数据重新构建搜索索引。当添加、删除或修改商品后需要执行此操作。
    - **加载现有索引**: 加载已存在的索引文件。通常在系统启动时自动执行。
    - **注意**: 索引构建可能需要一些时间，特别是在商品数量较多时。
    """)

if __name__ == "__main__":
    main()
